-- Create shared_queries table for public query sharing
CREATE TABLE shared_queries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    share_id VARCHAR(36) UNIQUE NOT NULL,     -- UUID format
    user_query_ids JSON NOT NULL,             -- Array of query IDs as JSON
    user_id VARCHAR(100) NOT NULL,            -- Original query owner (auth0_sub)
    title VARCHAR(255) NULL,                  -- Optional custom title for the share
    is_active BOOLEAN DEFAULT TRUE,           -- Can be disabled/revoked
    expires_at DATETIME NULL,                 -- Optional expiration
    view_count INT DEFAULT 0,                 -- Track popularity
    last_viewed_at DATETIME NULL,             -- Last access time
    created_at DATETIME DEFAULT NOW(),
    updated_at DATETIME DEFAULT NOW() ON UPDATE NOW(),

    INDEX idx_share_id (share_id),
    INDEX idx_user_id (user_id),
    INDEX idx_active_expires (is_active, expires_at)
);
